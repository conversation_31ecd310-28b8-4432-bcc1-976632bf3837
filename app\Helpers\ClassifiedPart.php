<?php

namespace App\Helpers;

class ClassifiedPart
{
    /**
     * Classify part demand based on stock-out history and current stock.
     *
     * @param array<int> $stockOutHistory  Monthly/Weekly stock-out quantities, e.g., [2, 3, 1, 0, 5, 6]
     * @param int        $stockNow         Current stock on hand
     *
     * @return string                      One of: 'High Demand', 'Stable', 'Low Demand', 'Seasonal', 'Overstocked', 'Uncategorized', 'Data Insufficient'
     */
    public static function classify(array $stockOutHistory, int $stockNow): string
    {
        // Constants (thresholds can be adjusted here)
        $MIN_REQUIRED_MONTHS = 4;
        $HIGH_DEMAND_AVG = 10;
        $STABLE_AVG_MIN = 1;
        $STABLE_AVG_MAX = 3;
        $LOW_DEMAND_AVG_MAX = 3;
        $OVERSTOCKED_MULTIPLIER = 6;

        $months = count($stockOutHistory);

        if ($months < $MIN_REQUIRED_MONTHS) {
            return 'Data Insufficient';
        }

        $averageUsage = array_sum($stockOutHistory) / $months;

        $variance = array_sum(array_map(
            fn($v) => pow($v - $averageUsage, 2),
            $stockOutHistory
        )) / $months;

        $stdDeviation = sqrt($variance);

        $usedMonths = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $usedPercent = $usedMonths / $months;

        // Classification Rules
        if (
            $averageUsage >= $HIGH_DEMAND_AVG &&
            $stdDeviation <= 2 &&
            $usedPercent > 0.8
        ) {
            return 'High Demand';
        }

        if (
            $averageUsage >= $STABLE_AVG_MIN &&
            $averageUsage <= $STABLE_AVG_MAX &&
            $usedPercent > 0.8 &&
            $stdDeviation <= 1.5
        ) {
            return 'Stable';
        }

        if (
            $usedPercent >= 0.7 &&
            $averageUsage < $LOW_DEMAND_AVG_MAX &&
            $stockNow < ($averageUsage * 2)
        ) {
            return 'Low Demand';
        }

        if (
            $stdDeviation > 5 &&
            $usedPercent <= 0.4 &&
            $averageUsage > 2
        ) {
            return 'Seasonal';
        }

        if (
            $stockNow > ($averageUsage * $OVERSTOCKED_MULTIPLIER) &&
            $averageUsage <= 1
        ) {
            return 'Overstocked';
        }

        return 'Uncategorized';
    }

    /**
     * Classify part demand based on stock-in/out history and current stock.
     *
     * @param array<int> $stockInHistory   Weekly stock-in quantities
     * @param array<int> $stockOutHistory  Weekly stock-out quantities
     * @param int        $stockNow         Current stock on hand
     *
     * @return string                      Classification result
     */
    public static function classifyWithInOut(array $stockInHistory, array $stockOutHistory, int $stockNow): string
    {
        $weeks = count($stockOutHistory);

        if ($weeks < 4 || count($stockInHistory) !== $weeks) {
            return 'Data Insufficient';
        }

        $totalIn = array_sum($stockInHistory);
        $totalOut = array_sum($stockOutHistory);
        $averageOut = $totalOut / $weeks;

        $usedWeeks = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $usedPercent = $usedWeeks / $weeks;

        // standard deviation
        $variance = array_sum(array_map(
            fn($v) => pow($v - $averageOut, 2),
            $stockOutHistory
        )) / $weeks;
        $stdDeviation = sqrt($variance);

        // pola out hanya awal''
        $outNonZeroIndexes = array_keys(array_filter($stockOutHistory));
        $outOnlyEarly = !empty($outNonZeroIndexes) && max($outNonZeroIndexes) < ($weeks * 0.3);

        // === Classification Rules ===

        // 1. Uncategorized: no movement, no stock
        if ($totalIn == 0 && $totalOut == 0 && $stockNow == 0) {
            return 'Uncategorized';
        }

        // 2. Dormant: no movement but stock still exists, or activity very low
        if (
            ($totalIn == 0 && $totalOut == 0 && $stockNow > 0) || // No activity, but stock exists
            ($stockNow > 0 && $totalOut < 5 && $usedPercent < 0.2) || // Very low out vs stock
            ($stockNow > 0 && $outOnlyEarly) // Only moved early, now idle
        ) {
            return 'Dormant';
        }

        // 3. Overstock: stock far exceeds average usage
        if ($averageOut > 0 && $stockNow > ($averageOut * $weeks * 1.5)) {
            return 'Overstock';
        }

        // 4. High Demand: consistently more out than in
        if (
            $totalOut > $totalIn &&
            $usedPercent > 0.8 &&
            $stdDeviation <= 2 &&
            $averageOut >= 8
        ) {
            return 'High Demand';
        }

        // 5. Stable: usage consistent, in ~ out
        if (
            abs($totalIn - $totalOut) <= ($weeks * 1.5) &&
            $stdDeviation <= 2 &&
            $usedPercent > 0.6
        ) {
            return 'Stable';
        }

        // 6. Low Demand: little but regular usage
        if (
            $averageOut <= 2 &&
            $usedPercent >= 0.3 &&
            $stockNow <= ($averageOut * 2)
        ) {
            return 'Low Demand';
        }

        // 7. Seasonal: usage peaks in few weeks only
        if (
            $usedPercent <= 0.3 &&
            $averageOut >= 5 &&
            $stdDeviation > 10
        ) {
            return 'Seasonal';
        }

        // 8. Default
        return 'Uncategorized';
    }
}
